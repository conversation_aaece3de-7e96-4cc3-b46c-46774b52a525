<template>
  <van-tabbar v-model="activeTab" active-color="#FA6D26" inactive-color="#818181" @change="onChange">
    <van-tabbar-item>
      <span>比赛</span>
      <template #icon="props">
        <img :src="props.active ? icon[0].active : icon[0].inactive" alt="比赛" />
      </template>
    </van-tabbar-item>
    <van-tabbar-item>
      <span>数据</span>
      <template #icon="props">
        <img :src="props.active ? icon[1].active : icon[1].inactive" alt="数据" />
      </template>
    </van-tabbar-item>
    <van-tabbar-item>
      <span>我的</span>
      <template #icon="props">
        <img :src="props.active ? icon[2].active : icon[2].inactive" alt="我的" />
      </template>
    </van-tabbar-item>
  </van-tabbar>
</template>

<script setup>
import { useRouter } from 'vue-router'
// 导入图片
import indexIcon from '@/static/tabbar/<EMAIL>'
import indexActiveIcon from '@/static/tabbar/<EMAIL>'
import newIcon from '@/static/tabbar/<EMAIL>'
import newActiveIcon from '@/static/tabbar/<EMAIL>'
import userIcon from '@/static/tabbar/<EMAIL>'
import userActiveIcon from '@/static/tabbar/<EMAIL>'

const router = useRouter()

const props = defineProps({
  active: {
    type: Number,
    default: 0
  }
})

const activeTab = ref(props.active)

const icon = [{
  active: indexActiveIcon,
  inactive: indexIcon
},
{
  active: newActiveIcon,
  inactive: newIcon
},
{
  active: userActiveIcon,
  inactive: userIcon
}]

const onChange = (index) => {
  switch (index) {
    case 0:
      router.push('/')
      break;
    case 1:
      router.push('/data')
      break;
    case 2:
      router.push('/user')
      break;
    default:
      break;
  }
}
</script>