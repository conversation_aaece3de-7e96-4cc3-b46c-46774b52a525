// composables/useAuth.ts
export const useAuth = () => {
  const userStore = useUserStore()
  
  // 登录函数
  const login = async (token: string, userInfo: any) => {
    userStore.setUserData(token, userInfo)
    
    // 可以在这里添加登录后的逻辑
    console.log('用户登录成功:', userInfo)
    
    return true
  }
  
  // 登出函数
  const logout = async () => {
    userStore.clearUserData()
    
    // 可以在这里添加登出后的逻辑，比如跳转到登录页
    console.log('用户已登出')
    
    // 跳转到登录页
    await navigateTo('/login/login')
    
    return true
  }
  
  // 检查是否已登录
  const isLoggedIn = computed(() => userStore.getIsLoggedIn)
  
  // 获取用户信息
  const userInfo = computed(() => userStore.getUserInfo)
  
  // 获取token
  const token = computed(() => userStore.getToken)
  
  // 刷新用户信息
  const refreshUserInfo = async () => {
    await userStore.updateUserInfo()
  }
  
  return {
    // 状态
    isLoggedIn,
    userInfo,
    token,
    
    // 方法
    login,
    logout,
    refreshUserInfo
  }
}
