<template>
  <div class="error-container">
    <div class="error-content">
      <h1>{{ error.statusCode }}</h1>
      <h2>{{ error.statusMessage }}</h2>
      
      <div v-if="error.statusCode === 401" class="auth-error">
        <p>您的登录已过期，请重新登录</p>
        <button @click="goToLogin" class="btn">去登录</button>
      </div>
      
      <div v-else class="general-error">
        <p>{{ error.message || '页面出现了一些问题' }}</p>
        <button @click="goHome" class="btn">返回首页</button>
      </div>
    </div>
  </div>
</template>

<script setup>
// 接收错误对象
const props = defineProps(['error'])

const goToLogin = () => {
  navigateTo('/login/login')
}

const goHome = () => {
  navigateTo('/')
}
</script>

<style scoped>
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 20px;
  background: #f5f5f5;
}

.error-content {
  text-align: center;
  background: white;
  padding: 40px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  max-width: 500px;
  width: 100%;
}

h1 {
  font-size: 4rem;
  color: #ff3b30;
  margin: 0;
}

h2 {
  font-size: 1.5rem;
  color: #333;
  margin: 10px 0 20px;
}

p {
  color: #666;
  margin-bottom: 20px;
}

.btn {
  padding: 12px 24px;
  background: #007aff;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
}

.btn:hover {
  background: #0056b3;
}
</style>
