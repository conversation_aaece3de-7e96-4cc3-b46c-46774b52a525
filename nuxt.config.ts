// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: '2025-05-15',
  devtools: { enabled: true },
  ssr: true,
  devServer: {
    host: "0.0.0.0", // 允许所有网络接口访问
    port: 3000, // 指定端口（可选）
  },
  imports: {
    dirs: [
      'composables/**', // 自动导入所有 composables
      'utils/**'       // 自动导入工具函数
    ]
  },
  runtimeConfig: {
    // 私有变量（仅服务端）
    // apiSecretKey: process.env.API_SECRET_KEY,
    // tokenRefreshUrl: process.env.TOKEN_REFRESH_URL,

    // 公共变量（客户端也可访问）
    public: {
      apiBaseUrl: process.env.NUXT_PUBLIC_API_BASE_URL || 'https://api.youliaobf.com'
    }
  },
  modules: [
    '@pinia/nuxt',
    '@vant/nuxt'
  ],
  app: {
    head: {
      title: "有料比分",
      titleTemplate: "%s",
      meta: [
        { charset: "utf-8" },
        { name: "viewport", content: "width=device-width, initial-scale=1, maximum-scale=1, viewport-fit=cover" },
        { name: 'robots', content: 'index, follow' },
        { name: 'googlebot', content: 'index, follow' },
        { name: 'baiduspider', content: 'index, follow' },
        { name: "description", content: "有料比分 - 有料比分是一个集足球比分、赛事数据、比赛情报、专家分析解读等服务于一体的综合性体育产品，为球迷提供覆盖全球的赛事分析，即时比分，购买查看其专业的赛事分析预测方案" },
        { name: "keywords", content: "有料比分,足球比分,篮球比分,足球预测,篮球预测,体育数据" },
        { name: "author", content: "有料比分" },
        { property: "title", content: "有料比分 - 专业的足球比分赛事预测分析平台" },
        { property: "description", content: "有料比分 - 有料比分是一个集足球比分、赛事数据、比赛情报、专家分析解读等服务于一体的综合性体育产品，为球迷提供覆盖全球的赛事分析，即时比分，购买查看其专业的赛事分析预测方案" },
        { property: "type", content: "website" },
        { property: "url", content: "https://h5.youliaobf.com" },
        // { property: "image", content: "https://h5.shenyantuling.com/assets/images/logo-black.png" },
        { property: "og:title", content: "有料比分 - 专业的足球比分赛事预测分析平台" },
        { property: "og:description", content: "有料比分 - 有料比分是一个集足球比分、赛事数据、比赛情报、专家分析解读等服务于一体的综合性体育产品，为球迷提供覆盖全球的赛事分析，即时比分，购买查看其专业的赛事分析预测方案" },
        { property: "og:type", content: "website" },
        { property: "og:url", content: "https://h5.youliaobf.com" },
        // { property: "og:image", content: "https://h5.shenyantuling.com/assets/images/logo-black.png" },
      ],
      link: [
        { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' }
      ]
    },
    pageTransition: { name: 'page', mode: 'out-in' }
  },
  // 忽略不需要跟踪的环境文件
  ignore: ['.env.local', '.env.*.local'],
  vite: {
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: '@import "@/assets/scss/variables.scss";' // 注入变量文件
        }
      }
    }
  }
})
