{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev --dotenv .env.development", "prod": "nuxt dev --dotenv .env.production", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@pinia/nuxt": "^0.11.0", "crypto-js": "^4.2.0", "nuxt": "^3.17.5", "pinia": "^3.0.3", "vconsole": "^3.15.1", "vue": "^3.5.16", "vue-router": "^4.5.1"}, "devDependencies": {"@vant/nuxt": "^1.0.7", "sass": "^1.89.1", "sass-loader": "^16.0.5", "vant": "^4.9.19"}}