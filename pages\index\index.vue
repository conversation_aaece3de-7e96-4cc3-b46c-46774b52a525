<template>
  <div class="container">
    <div class="tabbar">
      <van-nav-bar>
        <template v-slot:left>
          <div class="nav-slot-left">
            <img class="img" src="@/static/navbar/<EMAIL>"></img>
          </div>
        </template>
        <template v-slot:title>
          <div class="nav-slot-center">
            <span>比赛</span>
          </div>
        </template>
        <template v-slot:right>
          <div class="nav-slot-right">
            <img class="img" src="@/static/navbar/<EMAIL>"></img>
          </div>
        </template>
      </van-nav-bar>
    </div>
    <main class="match-list-box">
      <van-tabs v-model:active="fbCurrent" swipeable title-active-color="#FA6D26" color="#FA6D26">
        <van-tab v-for="item in fbTabList" :title="item.name">
          <div class="match-list-content">
            <van-list v-model:loading="loading" :finished="finished" finished-text="没有更多了">

            </van-list>
          </div>

        </van-tab>
      </van-tabs>
    </main>

    <!-- <div class="user-status">
      <p v-if="isLoggedIn">
        欢迎回来！用户ID: {{ userInfo?.id || '未知' }}
      </p>
      <p v-else>您还未登录</p>
    </div> -->

    <!-- <div class="actions">
      <button v-if="!isLoggedIn" @click="onPush" class="btn">去登录</button>
      <button v-else @click="goToProfile" class="btn">用户中心</button>
      <button v-if="isLoggedIn" @click="logout" class="btn btn-danger">退出登录</button>
      <button @click="goToTest" class="btn btn-test">测试API</button>
    </div> -->
    <Tabbar :active="0" />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useDayjs } from '#dayjs'
import { getScheduleListApi } from "@/utils/api/match";
import { useGamesStore } from '@/utils/stores/games';
import Tabbar from '@/components/Tabbar'
const dayjs = useDayjs()
const router = useRouter()
const gamesStore = useGamesStore()
const { isLoggedIn, userInfo, logout } = useAuth()
let fbTabList = reactive([
  // {
  // 	name: '重要',
  // 	type: 'IMPORTANT',
  // 	id: 0,
  // },
  {
    name: '全部',
    type: 'ALL',
    id: 4,
  },
  {
    name: '即时',
    type: 'REAL',
    id: 1,
  },
  {
    name: '竞足',
    type: 'FOOT',
    id: 2,
  },
  {
    name: '北单',
    type: 'BEIDAN',
    id: 3,
  },
  {
    name: '关注',
    type: 'FOCUS',
    id: 5,
  },
]);
let fbCurrent = ref(0)
let fbCurrentType = ref('ALL')
let fbChoiceDate = ref('')
const loading = ref(false);
const finished = ref(true);
const refreshing = ref(false);
const onPush = () => {
  router.push('/login');
}

const goToProfile = () => {
  router.push('/profile');
}

const goToTest = () => {
  router.push('/test-api');
}


//获取比赛数据
const getScheduleList = async () => {
  const currentTab = fbTabList[fbCurrent.value];
  const req = {
    queryEnum: currentTab.type,
    date: '' // 根据需求设置日期参数
  };
  if (fbCurrentType.value === 'ALL') {
    req.date = fbChoiceDate.value ? fbChoiceDate.value : dayjs().format('YYYY-MM-DD');
  } else {
    req.date = ''
  }
  let res = []
  try {
    res = await getScheduleListApi(req);
    return res;
  } catch (error) {
    return [];
  }
}
const getCachedFilters = (newList) => {
  const cachedFilters = {
    ALL: [],
    ONE: [],
    JC: [],
    BD: []
  };
  const source = JSON.parse(JSON.stringify(newList));

  // 预计算所有过滤类型
  cachedFilters.ALL = source;
  cachedFilters.ONE = source.map(item => ({
    ...item,
    datas: item.datas.filter(subItem => subItem.sclassGrade === 1)
  }));
  cachedFilters.JC = source.map(item => ({
    ...item,
    datas: item.datas.filter(subItem => subItem.isJc === 1)
  }));
  cachedFilters.BD = source.map(item => ({
    ...item,
    datas: item.datas.filter(subItem => subItem.isBd === 1)
  }));
  return cachedFilters
}
//切换swiper加载数据
const loadTabData = async (tabId, isRefresh = false) => {
  try {
    const res = await getScheduleList();
    gamesStore.set_now_swiper_data(res, tabId)
    //获取经过筛选的数据
    let filterData = gamesStore.get_index_filter_data();
    //格式化数据(分组)
    const formattedData = formatGroup(filterData);
    //保存筛选分组过的原始数据
    gamesStore.set_filter_data(formattedData, tabId)
    if (tabId === 'IMPORTANT' || tabId === 'ALL') {
      scrolltabData[tabId] = getCachedFilters(formattedData)
      tabData[tabId] = scrolltabData[tabId][operType.value]
    } else {
      tabData[tabId] = formattedData;
    }
    // getScreenFilters(res, tabId);
    // 存储到对应tab的独立数据空间
    loadedTabs.value.add(tabId);
    if (isRefresh) {
      endRefresh(tabId)
    } else {
      gamesLoaded(tabId)
    }
  } catch (error) {
    console.log(error);
    if (isRefresh) {
      endRefresh(tabId)
    }
  }
};
//过滤设置成每日一组数据
const formatGroup = (dataList) => {

  // 辅助函数
  const formatFullDate = (date) => {
    return `${date.getFullYear()}年${String(date.getMonth() + 1).padStart(2, '0')}月${String(date.getDate()).padStart(2, '0')}日`;
  };

  const getWeek = (date) => {
    const week = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    return week[date.getDay()];
  };

  const nowUTC = new Date();
  const groups = {};
  const padZero = (num) => num.toString().padStart(2, '0');
  const formatTime = (timeStr) => {
    if (!timeStr || timeStr.length !== 14) return '';
    const hours = timeStr.substring(8, 10);
    const minutes = timeStr.substring(10, 12);
    return `${padZero(hours)}:${padZero(minutes)}`;
  };
  dataList.forEach(item => {
    const matchTime = item.matchTime;
    const dateStr = matchTime.slice(0, 8);
    item.formatTime = formatTime(matchTime); // 使用matchTime而非item.matchTime（避免重复访问）
    const key = dateStr;
    if (!groups[key]) {
      groups[key] = { datas: [] };
    }
    groups[key].datas.push(item);
  });

  return Object.keys(groups).map(key => {
    // const year = parseInt(key.slice(0, 4));
    const month = parseInt(key.slice(4, 6)) - 1;
    const day = parseInt(key.slice(6, 8));

    // 强制使用当前年份，忽略年份差异
    const timeUTC = new Date(
      nowUTC.getFullYear(), // 当前年份
      month,
      day
    );

    // 计算日期差异（忽略年份）
    const nowDate = new Date(
      nowUTC.getFullYear(),
      nowUTC.getMonth(),
      nowUTC.getDate()
    );
    const timeDate = new Date(
      timeUTC.getFullYear(),
      timeUTC.getMonth(),
      timeUTC.getDate()
    );
    const diff = Math.floor(
      (timeDate.getTime() - nowDate.getTime()) / (1000 * 60 * 60 * 24)
    );

    let dataText = '';
    if (diff === 0) dataText = '今天';
    else if (diff === 1) dataText = '明天';
    else if (diff === -1) dataText = '昨天';

    return {
      day: dataText || '',
      date: formatFullDate(timeUTC),
      weeks: getWeek(timeUTC),
      scrollIndex: 0,
      datas: groups[key].datas
    };
  }).sort((a, b) => a.date.localeCompare(b.date));
};
onMounted(() => {
  loadTabData('ALL')
  loadedTabs.value.add('ALL'); // 防止重复加载
  gamesStore.set_now_swiper_type('ALL')
});
</script>

<style scoped lang="scss">
.nav-slot-left {
  .img {
    display: block;
    width: 24px;
    height: 24px;
  }
}

.nav-slot-center {
  display: flex;
  height: 100%;
  align-items: center;
  font-size: $bf-font-size-22;
  font-weight: 600;
}

.nav-slot-right {
  .img {
    display: block;
    width: 24px;
    height: 24px;
  }
}

.container {
  background-color: $bf-bg-primary;
}

.match-list-box {

  .match-list-content {
    height: calc(100vh - 140px);
    overflow: scroll;
  }
}

.user-status {
  margin: 20px 0;
  padding: 15px;
  background: #f0f0f0;
  border-radius: 8px;
}

.actions {
  margin-top: 20px;
}

.btn {
  padding: 10px 20px;
  margin: 0 10px;
  border: none;
  border-radius: 4px;
  background: #007aff;
  color: white;
  cursor: pointer;
}

.btn-danger {
  background: #ff3b30;
}

.btn-test {
  background: #34c759;
}
</style>