<template>
  <div class="container">
    <div class="tabbar">
      <van-nav-bar>
        <template v-slot:left>
          <div class="nav-slot-left">
            <img class="img" src="@/static/navbar/<EMAIL>"></img>
          </div>
        </template>
        <template v-slot:title>
          <div class="nav-slot-center">
            <span>比赛</span>
          </div>
        </template>
        <template v-slot:right>
          <div class="nav-slot-right">
            <img class="img" src="@/static/navbar/<EMAIL>"></img>
          </div>
        </template>
      </van-nav-bar>
    </div>
    <main class="match-list-box">
      <van-tabs v-model:active="fbCurrent" swipeable title-active-color="#FA6D26" color="#FA6D26">
        <van-tab v-for="item in fbTabList" :title="item.name">
          <div class="match-list-content">
            <van-list v-model:loading="loading" :finished="finished" finished-text="没有更多了">

            </van-list>
          </div>

        </van-tab>
      </van-tabs>
    </main>

    <!-- <div class="user-status">
      <p v-if="isLoggedIn">
        欢迎回来！用户ID: {{ userInfo?.id || '未知' }}
      </p>
      <p v-else>您还未登录</p>
    </div> -->

    <!-- <div class="actions">
      <button v-if="!isLoggedIn" @click="onPush" class="btn">去登录</button>
      <button v-else @click="goToProfile" class="btn">用户中心</button>
      <button v-if="isLoggedIn" @click="logout" class="btn btn-danger">退出登录</button>
      <button @click="goToTest" class="btn btn-test">测试API</button>
    </div> -->
    <Tabbar :active="0" />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useDayjs } from '#dayjs
import { getScheduleListApi } from "@/utils/api/match";
import Tabbar from '@/components/Tabbar'
const router = useRouter()
const { isLoggedIn, userInfo, logout } = useAuth()
let fbTabList = reactive([
  // {
  // 	name: '重要',
  // 	type: 'IMPORTANT',
  // 	id: 0,
  // },
  {
    name: '全部',
    type: 'ALL',
    id: 4,
  },
  {
    name: '即时',
    type: 'REAL',
    id: 1,
  },
  {
    name: '竞足',
    type: 'FOOT',
    id: 2,
  },
  {
    name: '北单',
    type: 'BEIDAN',
    id: 3,
  },
  {
    name: '关注',
    type: 'FOCUS',
    id: 5,
  },
]);
let fbCurrent = ref(0)
let fbCurrentType = ref('ALL')
let fbChoiceDate = ref('')
const loading = ref(false);
const finished = ref(true);
const refreshing = ref(false);
const onPush = () => {
  router.push('/login');
}

const goToProfile = () => {
  router.push('/profile');
}

const goToTest = () => {
  router.push('/test-api');
}


//获取比赛数据
const getScheduleList = async () => {
  const currentTab = fbTabList[fbCurrent.value];
  const req = {
    queryEnum: currentTab.type,
    date: '' // 根据需求设置日期参数
  };
  if (fbCurrentType.value === 'ALL') {
    req.date = fbChoiceDate.value ? fbChoiceDate.value : dayjs(new Date().getTime()).format('yyyy-mm-dd');
  } else {
    req.date = ''
  }
  let res = []
  try {
    res = await getScheduleListApi(req);
    return res;
  } catch (error) {
    return [];
  }
}
//切换swiper加载数据
const loadTabData = async (tabId, isRefresh = false) => {
  try {
    const res = await getScheduleList();
    gamesStore.set_now_swiper_data(res, tabId)
    //获取经过筛选的数据
    let filterData = gamesStore.get_index_filter_data();
    //格式化数据(分组)
    const formattedData = formatGroup(filterData);
    //保存筛选分组过的原始数据
    gamesStore.set_filter_data(formattedData, tabId)
    if (tabId === 'IMPORTANT' || tabId === 'ALL') {
      scrolltabData[tabId] = getCachedFilters(formattedData)
      tabData[tabId] = scrolltabData[tabId][operType.value]
    } else {
      tabData[tabId] = formattedData;
    }
    // getScreenFilters(res, tabId);
    // 存储到对应tab的独立数据空间
    loadedTabs.value.add(tabId);
    if (isRefresh) {
      endRefresh(tabId)
    } else {
      gamesLoaded(tabId)
    }
  } catch (error) {
    console.log(error);
    if (isRefresh) {
      endRefresh(tabId)
    }
  }
};
onMounted(() => {
  loadTabData('ALL')
});
</script>

<style scoped lang="scss">
.nav-slot-left {
  .img {
    display: block;
    width: 24px;
    height: 24px;
  }
}

.nav-slot-center {
  display: flex;
  height: 100%;
  align-items: center;
  font-size: $bf-font-size-22;
  font-weight: 600;
}

.nav-slot-right {
  .img {
    display: block;
    width: 24px;
    height: 24px;
  }
}

.container {
  background-color: $bf-bg-primary;
}

.match-list-box {

  .match-list-content {
    height: calc(100vh - 140px);
    overflow: scroll;
  }
}

.user-status {
  margin: 20px 0;
  padding: 15px;
  background: #f0f0f0;
  border-radius: 8px;
}

.actions {
  margin-top: 20px;
}

.btn {
  padding: 10px 20px;
  margin: 0 10px;
  border: none;
  border-radius: 4px;
  background: #007aff;
  color: white;
  cursor: pointer;
}

.btn-danger {
  background: #ff3b30;
}

.btn-test {
  background: #34c759;
}
</style>