<template>
  <div class="container">
    <h1>首页</h1>

    <div class="user-status">
      <p v-if="isLoggedIn">
        欢迎回来！用户ID: {{ userInfo?.id || '未知' }}
      </p>
      <p v-else>您还未登录</p>
    </div>

    <div class="actions">
      <button v-if="!isLoggedIn" @click="onPush" class="btn">去登录</button>
      <button v-else @click="goToProfile" class="btn">用户中心</button>
      <button v-if="isLoggedIn" @click="logout" class="btn btn-danger">退出登录</button>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()
const { isLoggedIn, userInfo, logout } = useAuth()

const onPush = () => {
  router.push('/login/login');
}

const goToProfile = () => {
  router.push('/profile');
}
</script>

<style scoped>
.container {
  padding: 20px;
  text-align: center;
}

.user-status {
  margin: 20px 0;
  padding: 15px;
  background: #f0f0f0;
  border-radius: 8px;
}

.actions {
  margin-top: 20px;
}

.btn {
  padding: 10px 20px;
  margin: 0 10px;
  border: none;
  border-radius: 4px;
  background: #007aff;
  color: white;
  cursor: pointer;
}

.btn-danger {
  background: #ff3b30;
}
</style>