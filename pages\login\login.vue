<template>
  <div class="container">
    <!-- 关闭按钮 -->
    <div class="close-icon" @click="onClose">
      <van-icon name="cross" color="#C2C2C2" size="28" />
    </div>

    <!-- logo -->
    <div class="logo">
      <img class="img" src="@/static/login/<EMAIL>" />
    </div>

    <!-- 登录方式标题 -->
    <div class="type-title">{{ loginType === 'CODE' ? '验证码登录' : '密码登录' }}</div>

    <!-- 表单区域 -->
    <div class="form-box">
      <!-- 验证码登录表单 -->
      <template v-if="loginType === 'CODE'">
        <van-field v-model="codeLoginReq.phone" placeholder="请输入手机号" type="tel" maxlength="11"
          @update:model-value="phoneChange" />
        <van-field v-model="codeLoginReq.smsCode" placeholder="请输入验证码" @update:model-value="inpChange">
          <template #button>
            <van-count-down v-if="isCounting" :time="60 * 1000" format="ss秒重新获取" @finish="isCounting = false" />
            <span class="get-code" :class="{ active: isPhone }" @click="getCode">{{ isCounting ? '' : '获取验证码' }}</span>
          </template>
        </van-field>
      </template>

      <!-- 密码登录表单 -->
      <template v-else>
        <van-field v-model="pwdLoginReq.phone" placeholder="请输入手机号" type="tel" maxlength="11"
          @update:model-value="pwdPhoneChange" />
        <van-field v-model="pwdLoginReq.password" placeholder="请输入密码" type="password" @update:model-value="inpChange">
          <template #button>
            <span class="get-code active" @click="onSetPwd">忘记密码</span>
          </template>
        </van-field>
      </template>

      <!-- 协议勾选 -->
      <div class="copy-right">
        <van-checkbox v-model="isRead" shape="round" checked-color="#FA6D26" @update:model-value="onChangeAgree">
          我已阅读并同意
          <span class="link-text" @click="onProtocol">《用户协议》</span>和
          <span class="link-text" @click="onPrivacy">《隐私政策》</span>
        </van-checkbox>
      </div>

      <!-- 登录按钮 -->
      <van-button class="submit" :class="{ active: isLogin }" @click="onLogin">登录 / 注册</van-button>
    </div>

    <!-- 其他登录方式 -->
    <div class="other-login">
      <van-divider>其他登录方式</van-divider>
      <div class="item" v-if="loginType === 'CODE'">
        <img @click="onChangeLoginType('PWD')" class="img" src="@/static/login/<EMAIL>" />
        <div class="label">密码登录</div>
      </div>
      <div class="item" v-if="loginType === 'PWD'">
        <img @click="onChangeLoginType('CODE')" class="img" src="@/static/login/<EMAIL>" />
        <div class="label">验证码登录</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onUnmounted } from 'vue'
import { getSmsCodeApi, userLoginByPhoneApi, userLoginByPwdApi } from '@/utils/api/user'
import { useRouter } from 'vue-router'

const router = useRouter()
const { login } = useAuth()

// 常量定义
const URL_PRIVACY = 'https://oss.youliaobf.com/prod/static/agreement_privacy.html'
const URL_USER_PROTOCOL = 'https://oss.youliaobf.com/prod/static/agreement_user.html'

// 响应式数据
const isLogin = ref(false)
const isCounting = ref(false)
const isPhone = ref(false)
const isPwdPhone = ref(false)
const isRead = ref(false)
const loginType = ref('CODE') // CODE-验证码登录 PWD-密码登录
const timeoutId = ref(null)

// 表单数据
const codeLoginReq = reactive({ phone: '', smsCode: '' })
const pwdLoginReq = reactive({ phone: '', password: '' })

// 手机号输入变化
const phoneChange = (number) => {
  isPhone.value = /^1[3-9]\d{9}$/.test(number)
  watchIsLogin()
}

// 密码登录手机号变化
const pwdPhoneChange = (number) => {
  isPwdPhone.value = /^1[3-9]\d{9}$/.test(number)
  watchIsLogin()
}

// 输入框变化
const inpChange = () => {
  watchIsLogin()
}

// 获取验证码
const getCode = async () => {
  if (!isPhone.value) {
    showToast('请输入正确的手机号')
    return
  }
  try {
    await getSmsCodeApi({ phone: codeLoginReq.phone })
    isCounting.value = true
    showToast('验证码已发送')
  } catch (error) {
    showToast(error.message || '验证码发送失败')
  }
}

// 登录/注册
const onLogin = async () => {
  if (!isLogin.value) return
  try {
    let res = loginType.value === 'CODE' ? await userLoginByPhoneApi(codeLoginReq) : await userLoginByPwdApi(pwdLoginReq)

    // 使用新的用户store保存登录信息
    await login(res.token, res.userDetail)

    showToast({ message: '登录成功', position: 'bottom' })
    timeoutId.value = setTimeout(() => {
      router.back()
    }, 2000)
  } catch (error) {
    showToast({ message: error.message || '登录失败', position: 'bottom' })
  }
}

// 检查是否可以登录
const watchIsLogin = () => {
  if (loginType.value === 'CODE') {
    isLogin.value = isRead.value && isPhone.value && codeLoginReq.smsCode.length > 0
  } else {
    isLogin.value = isRead.value && isPwdPhone.value && pwdLoginReq.password.length > 0
  }
}

// 切换登录方式
const onChangeLoginType = (type) => {
  loginType.value = type
  watchIsLogin()
}

// 协议勾选变化
const onChangeAgree = () => {
  watchIsLogin()
}

// 跳转忘记密码
const onSetPwd = () => {
  router.push('/reset-pwd')
}

// 查看用户协议
const onProtocol = () => {
  window.open(URL_USER_PROTOCOL)
}

// 查看隐私政策
const onPrivacy = () => {
  window.open(URL_PRIVACY)
}

// 关闭页面
const onClose = () => {
  router.back()
}

// 组件卸载时清除定时器
onUnmounted(() => {
  if (timeoutId.value) {
    clearTimeout(timeoutId.value)
  }
})
</script>

<style scoped lang="scss">
.container {
  width: 100%;
  min-height: 100vh;
  padding: 16px;
  box-sizing: border-box;
  background-color: white;
  position: relative;

  .close-icon {
    position: absolute;
    top: 16px;
    left: 16px;
    z-index: 1;
  }

  .logo {
    display: flex;
    justify-content: center;
    margin-top: 80px;

    .img {
      width: 90px;
      height: 90px;
    }
  }

  .type-title {
    font-size: 18px;
    font-weight: 500;
    text-align: center;
    margin-top: 30px;
    color: #333;
  }

  .form-box {
    margin-top: 24px;
    padding: 0 12px;

    .van-field {
      padding: 12px 0;
      margin-bottom: 16px;
      // box-sizing: border-box;
    }

    .get-code {
      font-size: 14px;
      color: #fa6d26;
      opacity: 0.5;

      &.active {
        opacity: 1;
        cursor: pointer;
      }
    }

    .copy-right {
      display: flex;
      align-items: center;
      margin-top: 12px;
      font-size: 12px;
      color: #c2c2c2;

      .link-text {
        color: #007aff;
        cursor: pointer;
      }
    }

    .submit {
      width: 100%;
      height: 44px;
      margin-top: 24px;
      border-radius: 22px;
      background: #fa6d26;
      border: none;
      color: white;
      opacity: 0.5;

      &.active {
        opacity: 1;

        &:active {
          opacity: 0.8;
        }
      }
    }
  }

  .other-login {
    width: 100%;
    position: fixed;
    bottom: 60px;
    left: 0;
    padding: 0 80px;
    box-sizing: border-box;

    .item {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-top: 20px;

      .img {
        width: 36px;
        height: 36px;
        cursor: pointer;
      }

      .label {
        margin-top: 8px;
        font-size: 12px;
        color: #c2c2c2;
      }
    }
  }
}
</style>