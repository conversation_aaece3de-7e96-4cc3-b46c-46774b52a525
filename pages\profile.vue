<template>
  <div class="profile-container">
    <h1>用户中心</h1>
    
    <div v-if="isLoggedIn" class="user-info">
      <h2>用户信息</h2>
      <p><strong>Token:</strong> {{ token ? '已设置' : '未设置' }}</p>
      <p><strong>用户ID:</strong> {{ userInfo?.id || '未知' }}</p>
      <p><strong>用户名:</strong> {{ userInfo?.name || '未知' }}</p>
      
      <div class="actions">
        <button @click="refreshUserInfo" class="btn">刷新用户信息</button>
        <button @click="logout" class="btn btn-danger">退出登录</button>
      </div>
    </div>
    
    <div v-else class="not-logged-in">
      <p>您还未登录</p>
      <button @click="goToLogin" class="btn">去登录</button>
    </div>
  </div>
</template>

<script setup>
// 使用中间件保护页面
definePageMeta({
  middleware: 'auth'
})

const { isLoggedIn, userInfo, token, logout, refreshUserInfo } = useAuth()

const goToLogin = () => {
  navigateTo('/login/login')
}
</script>

<style scoped>
.profile-container {
  padding: 20px;
  max-width: 600px;
  margin: 0 auto;
}

.user-info {
  background: #f5f5f5;
  padding: 20px;
  border-radius: 8px;
  margin-top: 20px;
}

.actions {
  margin-top: 20px;
}

.btn {
  padding: 10px 20px;
  margin-right: 10px;
  border: none;
  border-radius: 4px;
  background: #007aff;
  color: white;
  cursor: pointer;
}

.btn-danger {
  background: #ff3b30;
}

.not-logged-in {
  text-align: center;
  margin-top: 50px;
}
</style>
