<template>
  <div class="test-container">
    <h1>API测试页面</h1>

    <div class="test-section">
      <h2>测试请求拦截器</h2>
      <button @click="testGet" class="btn">测试GET请求</button>
      <button @click="testPost" class="btn">测试POST请求</button>
    </div>

    <div class="logs">
      <h3>请求日志：</h3>
      <div class="log-item" v-for="(log, index) in logs" :key="index">
        {{ log }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { $get, $post } from '~/utils/request'

const logs = ref([])

const addLog = (message) => {
  logs.value.unshift(`${new Date().toLocaleTimeString()}: ${message}`)
}

const testGet = async () => {
  addLog('开始GET请求测试...')
  addLog('请查看浏览器控制台的详细日志...')
  try {
    const result = await $get('/score-customer/login/sms.e', { phone: '13800138000' })
    addLog(`GET请求成功: ${JSON.stringify(result)}`)
  } catch (error) {
    addLog(`GET请求失败: ${error.message}`)
    console.error('GET请求详细错误:', error)
  }
}

const testPost = async () => {
  addLog('开始POST请求测试...')
  addLog('请查看浏览器控制台的详细日志...')
  try {
    const result = await $post('/score-customer/login/login.e', {
      phone: '13800138000',
      smsCode: '123456'
    })
    addLog(`POST请求成功: ${JSON.stringify(result)}`)
  } catch (error) {
    addLog(`POST请求失败: ${error.message}`)
    console.error('POST请求详细错误:', error)
  }
}
</script>

<style scoped>
.test-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-section {
  margin: 20px 0;
}

.btn {
  padding: 10px 20px;
  margin: 0 10px 10px 0;
  background: #007aff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.btn:hover {
  background: #0056b3;
}

.logs {
  margin-top: 30px;
  padding: 20px;
  background: #f5f5f5;
  border-radius: 8px;
  max-height: 400px;
  overflow-y: auto;
}

.log-item {
  padding: 5px 0;
  border-bottom: 1px solid #ddd;
  font-family: monospace;
  font-size: 14px;
}
</style>
