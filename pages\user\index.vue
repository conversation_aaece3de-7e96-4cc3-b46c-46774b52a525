<template>
  <div class="user-page">
    <div class="user-top" @click="gotoLogin">
      <template v-if="isLogin">
        <img class="user-avatar" :src="userInfo.userImage" alt="用户头像" />
        <span class="user-text">{{ userInfo.userName }}</span>
      </template>
      <template v-else>
        <img class="user-avatar" src="@/static/user/<EMAIL>" alt="默认用户头像" />
        <span class="user-text">登录/注册</span>
      </template>
      <img class="right-arrow-icon" src="@/static/user/<EMAIL>" alt="arrow" />
    </div>
    <div class="user-banner">
      <img class="user-banner-pic" src="@/static/user/<EMAIL>" alt="banner" />
    </div>
    <div class="user-bottom">
      <div class="user-bottom-icon1">
        <van-grid :column-num="4" :border="false">
          <van-grid-item v-for="(item, index) in iconList1" :key="index" @click="icon1Click(index)">
            <img class="grid-icon" :src="item.name" :alt="item.title" />
            <span class="grid-text">{{ item.title }}</span>
          </van-grid-item>
        </van-grid>
      </div>
      <div class="user-bottom-icon2">
        <van-grid :column-num="3" :border="false">
          <van-grid-item v-for="(item, index) in iconList2" :key="index" @click="icon2Click(index)">
            <img class="grid-icon" :src="item.name" :alt="item.title" />
            <span class="grid-text">{{ item.title }}</span>
          </van-grid-item>
        </van-grid>
      </div>
    </div>
    <!-- <copy-right></copy-right> -->
    <Tabbar :active="2" />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { isEmptyObject } from '@/utils/tools/tools'
import Tabbar from '@/components/Tabbar'
// 导入图片
import personalDataIcon from '@/static/user/<EMAIL>'
import shareIcon from '@/static/user/<EMAIL>'
import feedbackIcon from '@/static/user/<EMAIL>'
import contactUsIcon from '@/static/user/<EMAIL>'
import userAgreementIcon from '@/static/user/<EMAIL>'
import privacyPolicyIcon from '@/static/user/<EMAIL>'
import systemSettingIcon from '@/static/user/<EMAIL>'

const router = useRouter()

const URL_PRIVACY = "https://oss.youliaobf.com/prod/static/agreement_privacy.html"
const URL_USER_PROTOCOL = "https://oss.youliaobf.com/prod/static/agreement_user.html"

const iconList1 = reactive([
  {
    name: personalDataIcon,
    title: '个人资料'
  },
  {
    name: shareIcon,
    title: '分享App'
  },
  {
    name: feedbackIcon,
    title: '意见反馈'
  },
  {
    name: contactUsIcon,
    title: '联系我们'
  }
])

const iconList2 = reactive([
  {
    name: userAgreementIcon,
    title: '用户协议'
  },
  {
    name: privacyPolicyIcon,
    title: '隐私政策'
  },
  {
    name: systemSettingIcon,
    title: '系统设置'
  }
])

const userInfo = reactive({
  userImage: '',
  userName: ''
})

const isLogin = ref(false)

const gotoLogin = () => {
  if (isLogin.value) {
    router.push('/user/info')
  } else {
    router.push('/login')
  }
}

const icon1Click = (index) => {
  switch (index) {
    case 0:
      if (isLogin.value) {
        router.push('/user/info')
      } else {
        router.push('/login')
      }
      break;
    case 1:
      navigator.clipboard.writeText(window.location.origin)
        .then(() => {
          Dialog.alert({
            message: '已成功复制到粘贴板'
          })
        })
        .catch(() => {
          Dialog.alert({
            message: '复制失败！'
          })
        });
      break;
    case 2:
      router.push('/user/feedback')
      break;
    case 3:
      router.push({ path: '/user/about-contact', query: { type: 'contact' } })
      break;
    default:
      break;
  }
}

const icon2Click = (index) => {
  switch (index) {
    case 0:
      window.open(URL_USER_PROTOCOL, '_blank')
      break;
    case 1:
      window.open(URL_PRIVACY, '_blank')
      break;
    case 2:
      router.push('/user/settings')
      break;
    default:
      break;
  }
}

onMounted(() => {
  const info = useAuth.userInfo
  if (isEmptyObject(info)) {
    isLogin.value = false
  } else {
    isLogin.value = true
    Object.assign(userInfo, info)
  }
})
</script>

<style lang="scss" scoped>
.user-page {
  padding: 0 $bf-public-size-8;
  height: 100vh;
  background-color: $bf-bg-primary;

  .user-top {
    padding: 32px $bf-public-size-8 0;
    display: flex;
    align-items: center;

    .user-avatar {
      width: 70px;
      height: 70px;
      border-radius: 50%;
    }

    .user-text {
      font-size: $bf-font-size-18;
      font-weight: 400;
      color: $bf-text-dark;
      margin-left: $bf-public-size-12;
    }

    .right-arrow-icon {
      width: $bf-public-size-24;
      height: $bf-public-size-24;
      position: absolute;
      right: $bf-public-size-8;
    }
  }

  .user-banner {
    width: 100%;
    height: 120px;
    margin: $bf-public-size-16 0 $bf-public-size-8;

    &-pic {
      width: 100%;
      height: 120px;
    }
  }

  .user-bottom {

    &-icon2 {
      margin-top: 9px;
    }

    .grid-icon {
      width: $bf-public-size-32;
      height: $bf-public-size-32;
    }

    .grid-text {
      margin-top: $bf-public-size-4;
      font-size: $bf-font-size-base;
      font-weight: 400;
      color: $bf-text-dark;
    }
  }
}

::v-deep .van-grid {
  height: 88px;
  border-radius: $bf-public-size-16;
  overflow: hidden;
}
</style>