// utils/logger.ts
export const logger = {
  info: (message: string, ...args: any[]) => {
    if (import.meta.server) {
      console.log(`[SERVER INFO] ${message}`, ...args)
    } else {
      console.log(`[CLIENT INFO] ${message}`, ...args)
    }
  },
  
  warn: (message: string, ...args: any[]) => {
    if (import.meta.server) {
      console.warn(`[SERVER WARN] ${message}`, ...args)
    } else {
      console.warn(`[CLIENT WARN] ${message}`, ...args)
    }
  },
  
  error: (message: string, ...args: any[]) => {
    if (import.meta.server) {
      console.error(`[SERVER ERROR] ${message}`, ...args)
    } else {
      console.error(`[CLIENT ERROR] ${message}`, ...args)
    }
  },
  
  debug: (message: string, ...args: any[]) => {
    if (process.env.NODE_ENV === 'development') {
      if (import.meta.server) {
        console.debug(`[SERVER DEBUG] ${message}`, ...args)
      } else {
        console.debug(`[CLIENT DEBUG] ${message}`, ...args)
      }
    }
  }
}
