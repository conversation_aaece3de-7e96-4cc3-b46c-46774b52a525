// utils/api.ts
import { signRequest } from './sign';
import { useRuntimeConfig } from '#app'

export const createApiInstance = () => {
  const config = useRuntimeConfig()
  // 创建自定义请求实例
  return $fetch.create({
    baseURL: config.public.apiBaseUrl,
    onRequest({ options }) {

      // 确保 headers 存在
      if (!options.headers) {
        options.headers = {}
      }
      // 调用签名函数
      signRequest(options)
    },
    onResponse({ response }) {
      // 处理响应数据
      const data = response._data || response.data
      console.log('Response data:', data)

      // 自定义参数
      const custom = response.config?.custom || {};
      if (data?.code === 401) {
        if (process.client) {
          showToast(data.message);
          setTimeout(() => {
            // clearToken()
            navigateTo('/login/login')
          }, 1500)
        }
      }
      if (data?.code !== 0) { // 服务端返回的状态码不等于200，则reject()
        // 如果没有显式定义custom的toast参数为false的话，默认对报错进行toast弹出提示
        if (custom.toast !== false && process.client) {
          showToast(data.message);
        }
        // 如果需要catch返回，则进行reject
        if (custom?.catch) {
          return Promise.reject(data)
        } else {
          // 否则返回一个pending中的promise
          return new Promise(() => { })
        }
      }
      return data?.data || data || {}
    },
    onResponseError({ response }) {
      // 响应错误拦截器
      const errorMsg = response._data?.message || '请求失败'
      console.error(`[API Error] ${response.status}: ${errorMsg}`)
      return Promise.reject(response)
    }
  })

}

// Token 操作封装
export const setToken = (token) => {
  const tokenCookie = useCookie('token', { maxAge: 60 * 60 * 24 * 7 }) // 7天有效期
  tokenCookie.value = token
}

export const clearToken = () => {
  useCookie('token').value = null
}

// 封装常用请求方法
export const $get = (url, params) => {
  const config = useRuntimeConfig()

  // 构建请求选项
  const options = {
    method: 'GET',
    baseURL: config.public.apiBaseUrl,
    params,
    headers: {}
  }

  // 添加签名
  signRequest(options)
  return $fetch(url, options)
}

export const $post = (url, body) => {
  const config = useRuntimeConfig()

  // 构建请求选项
  const options = {
    method: 'POST',
    baseURL: config.public.apiBaseUrl,
    body,
    headers: {}
  }

  // 添加签名
  signRequest(options)

  return $fetch(url, options)
}
