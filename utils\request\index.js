// utils/api.ts
import { signRequest } from './sign';
import { useRuntimeConfig } from '#app'

export const createApiInstance = () => {
  const config = useRuntimeConfig()
  // 创建自定义请求实例
  return $fetch.create({
    baseURL: config.public.apiBaseUrl,
    onRequest({ options }) {
      // 请求拦截器：添加 Token
      options = signRequest(options)
      console.log('options:', options)
    },
    onResponse({ response }) {
      // 处理响应数据
      const data = response.data
      // 自定义参数
      const custom = response.config?.custom || {};
      if (data.code === 401) {
        if (process.client) {
          showToast(data.message);
          setTimeout(() => {
            // clearToken()
            navigateTo('/login/login')
          }, 1500)
        }
      }
      if (data.code !== 0) { // 服务端返回的状态码不等于200，则reject()
        // 如果没有显式定义custom的toast参数为false的话，默认对报错进行toast弹出提示
        if (custom.toast !== false && process.client) {
          showToast(data.message);
        }
        // 如果需要catch返回，则进行reject
        if (custom?.catch) {
          return Promise.reject(data)
        } else {
          // 否则返回一个pending中的promise
          return new Promise(() => { })
        }
      }
      return data.data || {}
    },
    onResponseError({ response }) {
      // 响应错误拦截器
      const errorMsg = response._data?.message || '请求失败'
      console.error(`[API Error] ${response.status}: ${errorMsg}`)
      return Promise.reject(response)
    }
  })

}

// Token 操作封装
export const setToken = (token) => {
  const tokenCookie = useCookie('token', { maxAge: 60 * 60 * 24 * 7 }) // 7天有效期
  tokenCookie.value = token
}

export const clearToken = () => {
  useCookie('token').value = null
}

// 封装常用请求方法
export const $get = (url, params) => {
  const api = createApiInstance()
  return api(url, { method: 'GET', params })
}

export const $post = (url, body) => {
  const api = createApiInstance()
  return api(url, { method: 'POST', body })
}
