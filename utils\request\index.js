// utils/api.ts
import { signRequest } from './sign';
import { useRuntimeConfig } from '#app'

export const createApiInstance = () => {
  const config = useRuntimeConfig()
  // 创建自定义请求实例
  return $fetch.create({
    baseURL: config.public.apiBaseUrl,
    onRequest({ request, options }) {
      console.log('onRequest called with:', { request, options })

      // 确保 headers 存在
      if (!options.headers) {
        options.headers = {}
      }

      // 构建signRequest期望的配置对象
      const signConfig = {
        url: request,
        method: options.method || 'GET',
        headers: options.headers,
        body: options.body,
        params: options.params
      }

      // 调用签名函数
      signRequest(signConfig)

      // 将签名后的headers复制回options
      options.headers = signConfig.headers

      console.log('After signRequest - options:', options)
      console.log('After signRequest - headers:', options.headers)
    },
    onResponse({ response }) {
      // 处理响应数据
      const data = response._data || response.data
      console.log('响应数据:', data)
      // 自定义参数
      const custom = response.config?.custom || {};
      //token失效
      if (data?.code === 401) {
        if (import.meta.client) {
          // 客户端：显示Toast并跳转
          showToast(data.message);
          setTimeout(() => {
            // 使用useAuth来清除用户数据
            const { logout } = useAuth()
            logout()
          }, 1500)
        } else {
          // 服务端：清除token并抛出错误

          // 服务端只清除token cookie，避免复杂的store操作
          console.warn('服务端检测到401未授权，清除token cookie')
          const tokenCookie = useCookie('token')
          tokenCookie.value = null

          // 在服务端直接抛出错误，让上层处理
          throw createError({
            statusCode: 401,
            statusMessage: data.message || '未授权访问'
          })
        }
      }
      if (data?.code !== 0) { // 服务端返回的状态码不等于200，则reject()
        // 如果没有显式定义custom的toast参数为false的话，默认对报错进行toast弹出提示
        if (custom.toast !== false) {
          if (import.meta.client) {
            // 客户端：显示Toast
            showToast(data.message);
          } else {
            // 服务端：记录错误日志
            console.error('API错误:', data.message, '状态码:', data.code);
          }
        }
        // 如果需要catch返回，则进行reject
        if (custom?.catch) {
          return Promise.reject(data)
        } else {
          // 否则返回一个pending中的promise
          return new Promise(() => { })
        }
      }
      return data?.data || data || {}
    },
    onResponseError({ response }) {
      console.log('响应错误1111:', response)
      // 响应错误拦截器
      return Promise.reject(response)
    }
  })

}

// 创建单例API实例
let apiInstance = null
const getApiInstance = () => {
  if (!apiInstance) {
    apiInstance = createApiInstance()
  }
  return apiInstance
}

// 封装常用请求方法
export const $get = (url, params) => {
  // 使用单例API实例
  const api = getApiInstance()
  return api(url, { method: 'GET', params })
}

export const $post = (url, body) => {
  // 使用单例API实例
  const api = getApiInstance()
  return api(url, { method: 'POST', body })
}
