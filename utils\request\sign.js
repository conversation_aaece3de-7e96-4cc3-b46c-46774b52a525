import CryptoJS from 'crypto-js';

export function signRequest(config) {
  // 确保 headers 存在
  if (!config.headers) {
    config.headers = {};
  }

  const timestamps = Date.now().toString();
  const key = 'shDOUArrDhpeAMw9FGY79Zmy3MLWwNWy';

  // 添加 timestamps 到 headers
  config.headers['timestamps'] = timestamps;

  // 获取参数字符串用于签名
  const test = loadParam(config);

  // 在函数内部获取token
  let token = '';
  if (import.meta.client) {
    try {
      token = useCookie('token').value || '';
    } catch (e) {
      console.warn('Failed to get token:', e);
    }
  }

  //添加token
  config.headers['token'] = token;
  config.headers['os'] = 'web';
  config.headers['businessType'] = 'score_customer';
  config.headers['codeChannel'] = 'youliaobf_h5';

  // 生成设备ID（替换uni-app的API）
  const deviceId = getDeviceId();
  config.headers['deviceId'] = deviceId;

  // AES 加密 timestamps
  const aesTimestamp = CryptoJS.AES.encrypt(
    timestamps,
    CryptoJS.enc.Utf8.parse("4d5bc50346c22dde12be2c3b1b89ada6"),
    {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.ZeroPadding
    }
  );
  const envTimestamps = aesTimestamp.toString();
  config.headers['envTimestamps'] = envTimestamps;

  // 计算签名
  const sign = CryptoJS.MD5(test + timestamps + key).toString(CryptoJS.enc.Hex).toLowerCase();
  config.headers['sign'] = sign;

  console.log('Sign request - test:', test);
  console.log('Sign request - timestamps:', timestamps);
  console.log('Sign request - sign:', sign);
  console.log('Sign request - headers:', config.headers);

  return config;
}

function loadParam(config) {
  let str = "";
  const contentType = config.headers?.['Content-Type'] || config.headers?.['content-type'] || '';

  if (contentType === 'text/event-stream') {
    const url = config.url;
    if (url && url.indexOf('?') >= 0) {
      str = url.substring(url.indexOf('?') + 1, url.length);
    }
  } else if (contentType !== 'multipart/form-data') {
    if (config.body || config.params) {
      if (config.method === 'GET') {
        if (config.params) {
          let params = config.params;
          const keyList = Object.keys(config.params);
          // 解析 GET 请求的查询参数
          for (const key of keyList) {
            str += keyList[keyList.length - 1] !== key
              ? `${key}=${encodeURI(params[key])}&`
              : `${key}=${encodeURI(params[key])}`;
          }
        }
      } else {
        if (config.body) {
          str = JSON.stringify(config.body);
        }
      }
    }
  } else {
    const url = config.url;
    if (url && url.indexOf('?') >= 0) {
      str = url.substring(url.indexOf('?') + 1, url.length);
    }
  }
  return str;
}

// 生成设备ID的函数
function getDeviceId() {
  if (import.meta.client) {
    // 尝试从localStorage获取已存储的设备ID
    let deviceId = localStorage.getItem('deviceId');
    if (!deviceId) {
      // 生成新的设备ID
      deviceId = 'web_' + Math.random().toString(36).substring(2, 11) + Date.now().toString(36);
      localStorage.setItem('deviceId', deviceId);
    }
    return deviceId;
  }
  // 服务端返回默认值
  return 'web_server';
}