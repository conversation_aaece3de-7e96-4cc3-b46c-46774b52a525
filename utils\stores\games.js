import {
  defineStore
} from 'pinia';
import { getServiceTimeApi } from '@/utils/api/match';
import { useNoticeStore } from './notice';
import ALL from '@/static/game-list/<EMAIL>'
import ONE from '@/static/game-list/<EMAIL>';
import JC from '@/static/game-list/<EMAIL>';
import BD from '@/static/game-list/<EMAIL>';

// 合并分组数据（如classs/countrys）
const mergeGroups = (oldGroups, newGroups) => {
  return newGroups.map(newGroup => {
    const oldGroup = oldGroups?.find(g => g.letter === newGroup.letter);
    const items = newGroup.items.map(item => {
      const oldItem = (oldGroup?.items || []).find(i => i.name === item.name);
      return oldItem ? { ...item, checked: oldItem.checked } : item;
    });
    return { ...newGroup, items };
  });
};

// 合并非分组数据（如cshandicaps/csoverUnder）
const mergeItems = (oldItems, newItems) => {
  return newItems.map(item => {
    const oldItem = oldItems?.find(i => i.value === item.value);
    return oldItem ? { ...item, checked: oldItem.checked } : item;
  });
};

// 主合并函数
const mergeData = (oldData, newData) => {
  return {
    ...newData,
    countrys: mergeGroups(oldData?.countrys, newData.countrys),
    classs: mergeGroups(oldData?.classs, newData.classs),
    classesOne: mergeGroups(oldData?.classesOne, newData.classesOne),
    classesJc: mergeGroups(oldData?.classesJc, newData.classesJc),
    classesBd: mergeGroups(oldData?.classesBd, newData.classesBd),
    cshandicaps: mergeItems(oldData?.cshandicaps, newData.cshandicaps),
    csoverUnder: mergeItems(oldData?.csoverUnder, newData.csoverUnder),
  };
};


const getMiddleValue = (indexLet) => {
  if (!indexLet || typeof indexLet !== 'string') return '无指数';
  const parts = indexLet.split(',');
  return parts.length >= 3 ? parts[1].trim() : '';
};
//分组筛选条件
// type = exponentType=即时 REAL 初始 INIT
const getScreenFilters = (matchList, type) => {
  const collect = {
    countries: [], // 存储完整的国家对象 { name, pinyin }
    classes: [],   // 存储完整的联赛对象 { name, pinyin }
    classesOne: [],
    classesJc: [],
    classesBd: [],
    handicaps: new Set(),
    overUnder: new Set()
  };
  matchList.filter((item) => {
    return item.sclassGrade === 1;
  }).forEach((item) => {
    if (item.sclassName && item.pinyinSclass) {
      collect.classesOne.push({
        name: item.sclassName,
        pinyin: item.pinyinSclass.trim().toUpperCase(), // 统一转大写处理
        isSclassHot: item.isSclassHot,
      })
    }
  })
  matchList.filter((item) => {
    return item.isJc === 1;
  }).forEach((item) => {
    if (item.sclassName && item.pinyinSclass) {
      collect.classesJc.push({
        name: item.sclassName,
        pinyin: item.pinyinSclass.trim().toUpperCase(), // 统一转大写处理
        isSclassHot: item.isSclassHot,
      })
    }
  })
  matchList.filter((item) => {
    return item.isBd === 1;
  }).forEach((item) => {
    if (item.sclassName && item.pinyinSclass) {
      collect.classesBd.push({
        name: item.sclassName,
        pinyin: item.pinyinSclass.trim().toUpperCase(), // 统一转大写处理
        isSclassHot: item.isSclassHot,
      })
    }
  })
  // 数据收集阶段
  for (const item of matchList) {
    // 国家收集（保留拼音首字母）
    if (item.countryName && item.pinyinCountry) {
      collect.countries.push({
        name: item.countryName,
        pinyin: item.pinyinCountry.trim().toUpperCase(), // 统一转大写处理
      });
    }

    // 联赛收集（保留拼音首字母）
    if (item.sclassName && item.pinyinSclass) {
      collect.classes.push({
        name: item.sclassName,
        pinyin: item.pinyinSclass.trim().toUpperCase(), // 统一转大写处理
        isSclassHot: item.isSclassHot,
      });
    }

    // 让球/大小球处理
    // if (type === 'REAL') {
    const handicap = getMiddleValue(item.indexLet);
    if (handicap) collect.handicaps.add(handicap);
    const overUnder = getMiddleValue(item.indexTotal);
    if (overUnder) collect.overUnder.add(overUnder);
    // }
    //注释即时，初始指数切换 只用即时
    // if (type === 'INIT') {
    //   const handicap = getMiddleValue(item.firstIndexLet);
    //   if (handicap) collect.handicaps.add(handicap);
    //   const overUnder = getMiddleValue(item.firstIndexTotal);
    //   if (overUnder) collect.overUnder.add(overUnder);
    // }

  }

  // 生成首字母列表并排序
  const countryInitials = Array.from(
    new Set(collect.countries.map(c => c.pinyin[0]))
  )
    .filter(letter => /^[A-Z]$/.test(letter))
    .sort();

  // 在 collect 数据收集后，生成热门赛事组
  const createHotGroup = (collectKey) => {
    const items = collect[collectKey]
      .filter(c => c.isSclassHot === 1)
      .map(c => c.name)
      .sort()
      .filter((item, index, arr) => arr.indexOf(item) === index);
    return {
      group: {
        letter: '热',
        items: items.map(name => ({ name, checked: true, isHot: true })),
      },
      hotNames: items
    };
  };

  const hotClassesResult = createHotGroup('classes');
  const hotGroupClasses = hotClassesResult.group;
  const hotNamesClasses = hotClassesResult.hotNames;

  const hotOneResult = createHotGroup('classesOne');
  const hotGroupOne = hotOneResult.group;
  const hotNamesOne = hotOneResult.hotNames;

  const hotJcResult = createHotGroup('classesJc');
  const hotGroupJc = hotJcResult.group;
  const hotNamesJc = hotJcResult.hotNames;

  const hotBdResult = createHotGroup('classesBd');
  const hotGroupBd = hotBdResult.group;
  const hotNamesBd = hotBdResult.hotNames;
  collect.classes = collect.classes.filter(c => !hotNamesClasses.includes(c.name));
  const classInitials = Array.from(
    new Set(collect.classes.map(c => c.pinyin[0]))
  )
    .filter(letter => /^[A-Z]$/.test(letter))
    .sort();
  collect.classesOne = collect.classesOne.filter(c => !hotNamesOne.includes(c.name));
  const classOneInitials = Array.from(
    new Set(collect.classesOne.map(c => c.pinyin[0]))
  )
    .filter(letter => /^[A-Z]$/.test(letter))
    .sort();
  collect.classesJc = collect.classesJc.filter(c => !hotNamesJc.includes(c.name));
  const classJcInitials = Array.from(
    new Set(collect.classesJc.map(c => c.pinyin[0]))
  )
    .filter(letter => /^[A-Z]$/.test(letter))
    .sort();
  collect.classesBd = collect.classesBd.filter(c => !hotNamesBd.includes(c.name));
  const classBdInitials = Array.from(
    new Set(collect.classesBd.map(c => c.pinyin[0]))
  )
    .filter(letter => /^[A-Z]$/.test(letter))
    .sort();

  // 按首字母分组并去重
  const countryGroups = countryInitials.map(letter => ({
    letter,
    items: Array.from(
      new Set( // 分组时去重
        collect.countries
          .filter(c => c.pinyin.startsWith(letter))
          .map(c => c.name)
          .sort()
      )
    ).map((name) => {
      return { name, checked: true };
    })
  }));
  const classGroups = classInitials.map(letter => ({
    letter,
    items: Array.from(
      new Set( // 分组时去重
        collect.classes
          .filter(c => c.pinyin.startsWith(letter))
          .map(c => c.name)
          .sort()
      )
    ).map((name) => {
      // let ind = hotGroupClasses.items.findIndex((item) => {
      //   return item.name === name
      // })
      return { name, checked: true }
    })
  }));
  classGroups.unshift(hotGroupClasses)
  const classOneGroups = classOneInitials.map(letter => ({
    letter,
    items: Array.from(
      new Set( // 分组时去重
        collect.classesOne
          .filter(c => c.pinyin.startsWith(letter))
          .map(c => c.name)
          .sort()
      )
    ).map((name) => {
      // let ind = hotGroupOne.items.findIndex((item) => {
      //   return item.name === name
      // })
      return { name, checked: true }
    })
  }));
  classOneGroups.unshift(hotGroupOne)
  const classJcGroups = classJcInitials.map(letter => ({
    letter,
    items: Array.from(
      new Set( // 分组时去重
        collect.classesJc
          .filter(c => c.pinyin.startsWith(letter))
          .map(c => c.name)
          .sort()
      )
    ).map((name) => {
      return { name, checked: true }
    })
  }));
  classJcGroups.unshift(hotGroupJc)
  const classBdGroups = classBdInitials.map(letter => ({
    letter,
    items: Array.from(
      new Set( // 分组时去重
        collect.classesBd
          .filter(c => c.pinyin.startsWith(letter))
          .map(c => c.name)
          .sort()
      )
    )
      .map((name) => {
        // let ind = hotGroupBd.items.findIndex((item) => {
        //   return item.name === name
        // })
        return { name, checked: true }
      })
  }));
  classBdGroups.unshift(hotGroupBd);
  return {
    countryInitials,
    classInitials,
    classOneInitials,
    classJcInitials,
    classBdInitials,
    countrys: countryGroups,
    classs: classGroups,
    classesOne: classOneGroups,
    classesJc: classJcGroups,
    classesBd: classBdGroups,
    cshandicaps: Array.from(collect.handicaps).map(v => ({ value: v, checked: true })),
    csoverUnder: Array.from(collect.overUnder).map(v => ({ value: v, checked: true }))
  };
}

export const useGamesStore = defineStore('games', {
  state: () => {
    return {
      //serverTime时间 
      serverTime: 0,
      syncTimer: null,
      nowSwiperType: 'REAL',
      nowSwiperData: {},
      //获取过滤过的数据
      filterData: {},
      //即时 REAL 初始 INIT
      exponentType: uni.getStorageSync('exponentType') || 'REAL',
      isShowExponent: uni.getStorageSync('isShowExponent') || true,
      //筛选数据
      screenData: {},
      //过滤过后的数据
      // screenfilterData: [],
      screenTabActive: 'classs',
      screenTabConfirmActive: {
        ALL: {
          topActive: '',
          footerActive: ''
        },
        REAL: {
          topActive: '',
          footerActive: ''
        },
        FOOT: {
          topActive: '',
          footerActive: ''
        },
        BEIDAN: {
          topActive: '',
          footerActive: ''
        },
        FOCUS: {
          topActive: '',
          footerActive: ''
        }
      },
      screenTabConfirmData: [],
      screenTabList: [
        {
          name: '赛事',
          type: 'classs',
          id: 0,
        },
        {
          name: '让球',
          type: 'cshandicaps',
          id: 1,
        },
        {
          name: '进球数',
          type: 'csoverUnder',
          id: 2,
        },
        {
          name: '国家',
          type: 'countrys',
          id: 3,
        }
      ],
      scrollTabActive: 'ALL',
      scrollTabList: [
        {
          index: 0,
          type: 'ALL',
          image: ALL,
          value: '全部'
        },
        {
          index: 1,
          type: 'ONE',
          image: ONE,
          value: '一级'
        },
        {
          index: 2,
          type: 'JC',
          image: JC,
          value: '竞足'
        },
        {
          index: 3,
          type: 'BD',
          image: BD,
          value: '北单'
        }
      ]
    };
  },
  getters: {
    //获取用户token
    get_server_time() {
      return this.serverTime;
    },
    get_exponent_type() {
      return this.exponentType;
    },
    is_show_exponent() {
      return this.isShowExponent;
    },
    get_now_swiper_data() {
      return this.nowSwiperData[this.nowSwiperType];
    },
  },
  actions: {
    //获取服务器时间
    async getServerTime() {
      try {
        const data = await getServiceTimeApi()
        this.serverTime = data.currentDateLong;
        this.initServerTimeSync()
      } catch (error) {
        console.error('获取服务器时间失败:', error)
      }
    },
    // 启动全局同步
    initServerTimeSync() {
      let loaclTime = 0
      this.syncTimer = setInterval(() => {
        this.serverTime += 2000;
        loaclTime += 2000;
        if (loaclTime > 120000) {
          this.destroySync()
          this.serverTime = 0;
          this.getServerTime()
        }
      }, 2000) // 每一分钟同步
    },
    // 销毁定时器
    destroySync() {
      clearInterval(this.syncTimer)
    },
    set_exponent_type(type) {
      this.exponentType = type;
      uni.setStorageSync('exponentType', type);
    },
    set_is_show_exponent(status) {
      this.isShowExponent = status;
      uni.setStorageSync('isShowExponent', status);
    },
    //设置北单一级竞足当前筛选状态
    set_scroll_tab_active(active) {
      this.scrollTabActive = active;
    },
    //筛选过滤初始数据
    set_filter_data(data, type) {
      //设置筛选过滤初始数据
      this.filterData[type] = data;
    },
    //首页获取经过右上角筛选后的数据
    get_index_filter_data() {
      //判断当前列是否有筛选数据
      let active = this.get_screen_tab_confirm_topactive()
      if (active == '') {
        return this.nowSwiperData[this.nowSwiperType];
      } else {
        let data = this.filterSelected(active)
        return data;
      }
    },
    //筛选页更新筛选数据
    updateFilterSelected() {
      let active = this.getScreenActive();
      return this.filterSelected(active)
    },
    //获取当前筛选页选中的类型
    getScreenActive() {
      let topActive = this.screenTabActive;
      // ALL ONE JC BD
      let bottomActive = this.scrollTabActive;
      let active = ''
      if (topActive === 'classs') {
        if (bottomActive === 'ALL') {
          active = 'classs'
        } else {
          active = bottomActive
        }
      } else {
        active = topActive;
      }
      return active;
    },
    /**
     * 
     * @param {*} active 赛事 让球 进球 国家
     * @returns 
     */
    filterSelected(active) {
      // isScreen 判断是在筛选页有无 竞足，北单
      let filterGameList = [];
      let gameList = this.nowSwiperData[this.nowSwiperType];
      if (active === 'ONE') {
        const selected = this.get_screen_data().classesOne.flatMap(group => group.items)
          .filter(item => item.checked)
          .map(item => item.name);
        const selectedSet = new Set(selected);
        filterGameList = gameList.filter(
          item => selectedSet.has(item.sclassName)
        );
      }
      if (active === 'JC') {
        const selected = this.get_screen_data().classesJc.flatMap(group => group.items)
          .filter(item => item.checked)
          .map(item => item.name);
        const selectedSet = new Set(selected);
        filterGameList = gameList.filter(
          item => selectedSet.has(item.sclassName)
        );
      }
      if (active === 'BD') {
        const selected = this.get_screen_data().classesBd.flatMap(group => group.items)
          .filter(item => item.checked)
          .map(item => item.name);
        const selectedSet = new Set(selected);
        filterGameList = gameList.filter(
          item => selectedSet.has(item.sclassName)
        );
      }
      if (active === 'classs') {
        const selected = this.get_screen_data().classs.flatMap(group => group.items)
          .filter(item => item.checked)
          .map(item => item.name);
        const selectedSet = new Set(selected);
        filterGameList = gameList.filter(
          item => selectedSet.has(item.sclassName)
        );
      }
      if (active === 'countrys') {
        const selected = this.get_screen_data().countrys.flatMap(group => group.items)
          .filter(item => item.checked)
          .map(item => item.name);
        const selectedSet = new Set(selected);
        filterGameList = gameList.filter(
          item => selectedSet.has(item.countryName)
        );
      }
      if (active === 'cshandicaps') {
        const selected = this.get_screen_data().cshandicaps
          .filter(item => item.checked)
          .map(item => item.value);
        const selectedSet = new Set(selected);
        filterGameList = gameList.filter(item => {
          let middleValue = '';
          //指数只取即时不要初始
          // if (this.exponentType === 'REAL') {
          middleValue = getMiddleValue(item.indexLet);
          // } else {
          //   middleValue = getMiddleValue(item.firstIndexLet);
          // }
          return selectedSet.has(middleValue);
        });
      }
      if (active === 'csoverUnder') {
        const selected = this.get_screen_data().csoverUnder
          .filter(item => item.checked)
          .map(item => item.value);
        const selectedSet = new Set(selected);
        filterGameList = gameList.filter(item => {
          let middleValue = '';
          //指数只取即时不要初始
          // if (this.exponentType === 'REAL') {
          middleValue = getMiddleValue(item.indexTotal);
          // } else {
          //   middleValue = getMiddleValue(item.firstIndexTotal);
          // }
          return selectedSet.has(middleValue);
        });
      }
      return filterGameList;
    },
    //获取当前页面展示的数据
    get_screen_data() {
      return this.screenData[this.nowSwiperType];
    },
    set_screen_data(data) {
      let oldScreenData = this.screenData[this.nowSwiperType] || {};

      //如何当前没有筛选条件把老数据置空
      if (this.get_screen_tab_confirm_topactive() === '') {
        oldScreenData = {}
      }
      const newData = getScreenFilters(data, this.exponentType);
      const mergedData = mergeData(oldScreenData, newData);
      this.screenData[this.nowSwiperType] = mergedData;
    },
    set_screen_tab_confirm_data(data) {
      this.screenTabConfirmData = data;
    },
    //设置当首页是在哪一块
    set_now_swiper_type(type) {
      this.nowSwiperType = type;
    },
    //设置当前首页数据以及筛选
    set_now_swiper_data(data, type) {
      const noticeStore = useNoticeStore()
      //判断如果是关注列表的话，缓存数据，通知弹窗会使用
      if (type === 'FOCUS') {
        noticeStore.focusList = data;
      }
      this.nowSwiperData[type] = data;
      this.set_screen_data(data)
    },

    //筛选标签active
    set_screen_tab_active(active) {
      this.screenTabActive = active;
    },
    //设置筛选点击确认标签 topActive 顶部一级,footerActive 赛事底部切换
    set_screen_tab_confirm_active(topActive, footerActive = 'ALL') {
      this.screenTabConfirmActive[this.nowSwiperType].topActive = topActive;
      this.screenTabConfirmActive[this.nowSwiperType].footerActive = footerActive;
    },
    //获取topActive
    get_screen_tab_confirm_topactive() {
      return this.screenTabConfirmActive[this.nowSwiperType].topActive ? this.screenTabConfirmActive[this.nowSwiperType].topActive : ''
    },
    //获取footerActive
    get_screen_tab_confirm_footeractive() {
      return this.screenTabConfirmActive[this.nowSwiperType].footerActive ? this.screenTabConfirmActive[this.nowSwiperType].footerActive : 'ALL'
    },

  },
});