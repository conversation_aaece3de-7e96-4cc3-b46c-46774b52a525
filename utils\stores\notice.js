import {
  defineStore
} from 'pinia';
import { getScheduleListApi } from '@/utils/api/match';
// { name: '默认', type: 'DEFAULT' },
// { name: '哨子', type: 'WHISTLE' },
// { name: '胜利', type: 'WIN' },
// { name: '号角', type: 'BUGLE' },
// { name: '鼓声', type: 'DRUMROLL' },
export const useNoticeStore = defineStore('notice', {
  state: () => {
    return {
      //是否显示通知 
      isShowNotice: uni.getStorageSync('isShowNotice') || true,
      //提示范围 FOLLOW ALL
      followOrAll: uni.getStorageSync('followOrAll') || 'FOLLOW',
      // 显示范围 LIST ALL
      listOrAll: uni.getStorageSync('listOrAll') || 'LIST',
      //是否播放声音
      isAudio: uni.getStorageSync('isAudio') || true,
      //主场音频
      homeAudio: uni.getStorageSync('homeAudio') || 'DEFAULT',
      //客场音频
      awayAudio: uni.getStorageSync('awayAudio') || 'DEFAULT',
      //是否红牌提示
      isRedTip: uni.getStorageSync('isRedTip') || false,
      focusList: []
    };
  },
  getters: {
    is_show_notice() {
      return this.isShowNotice;
    },
  },
  actions: {
    set_is_show_notice(status) {
      this.isShowNotice = status;
      uni.setStorageSync('isShowNotice', status);
    },
    set_follow_or_all(status) {
      this.followOrAll = status;
      uni.setStorageSync('followOrAll', status);
    },
    set_list_or_all(status) {
      this.listOrAll = status;
      uni.setStorageSync('listOrAll', status);
    },
    set_is_audio(status) {
      this.isAudio = status;
      uni.setStorageSync('isAudio', status);
    },
    set_home_audio(status) {
      this.homeAudio = status;
      uni.setStorageSync('homeAudio', status);
    },
    set_away_audio(status) {
      this.awayAudio = status;
      uni.setStorageSync('awayAudio', status);
    },
    set_is_red_tip(status) {
      this.isRedTip = status;
      uni.setStorageSync('isRedTip', status);
    },
    //获取关注列表
    async getFocusList() {
      try {
        let req = {
          queryEnum: 'FOCUS'
        }
        let res = await getScheduleListApi(req);
        console.log(res, 'focusList')
        this.focusList = Object.assign([], res)
      } catch (error) {
        console.error('focusList:', error)
      }
    },
  },
});