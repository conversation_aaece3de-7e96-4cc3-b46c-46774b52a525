import {
  defineStore
} from 'pinia';
import { getBaseUserInfoApi } from '@/utils/api/user';

export const useUserStore = defineStore('user', {
  state: () => {
    return {
      userInfo: {}
    }
  },
  actions: {
    //存储用户信息
    set_user_info(userInfo) {
      this.userInfo = userInfo;
    },
    //删除用户信息
    delete_user_info() {
      this.userToken = '';
      this.userInfo = {};
    },
    //更新用户信息
    async updateUserInfo() {
      let data = await getBaseUserInfoApi()
      this.set_user_info(data);
    }
  }
})