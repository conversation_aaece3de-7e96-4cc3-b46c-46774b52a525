import { defineStore } from 'pinia';
// import { getBaseUserInfoApi } from '@/utils/api/user';

export const useUserStore = defineStore('user', {
  state: () => {
    return {
      userToken: '',
      userInfo: {},
      isLoggedIn: false
    }
  },

  getters: {
    // 获取用户是否已登录
    getIsLoggedIn: (state) => {
      return !!state.userToken && !!state.userInfo?.id
    },

    // 获取用户信息
    getUserInfo: (state) => state.userInfo,

    // 获取用户token
    getToken: (state) => state.userToken
  },

  actions: {
    // 初始化store（从持久化存储中恢复数据）
    async initStore() {
      if (import.meta.client) {
        // 客户端：从localStorage恢复数据
        try {
          const token = localStorage.getItem('userToken')
          const userInfo = localStorage.getItem('userInfo')

          if (token) {
            this.userToken = token
            // 同步到cookie
            const tokenCookie = useCookie('token', {
              maxAge: 60 * 60 * 24 * 7,
              sameSite: 'lax',
              secure: false
            })
            tokenCookie.value = token
          }

          if (userInfo) {
            this.userInfo = JSON.parse(userInfo)
          }

          this.isLoggedIn = this.getIsLoggedIn
        } catch (error) {
          console.error('客户端初始化用户store失败:', error)
          this.clearUserData()
        }
      } else {
        // 服务端：从cookie恢复token
        try {
          const tokenCookie = useCookie('token')
          if (tokenCookie.value) {
            this.userToken = tokenCookie.value
            this.isLoggedIn = !!tokenCookie.value
            console.log('服务端从cookie恢复token:', !!tokenCookie.value)
          }
        } catch (error) {
          console.error('服务端初始化用户store失败:', error)
        }
      }
    },

    // 设置用户token
    setUserToken(token) {
      this.userToken = token
      this.isLoggedIn = !!token

      if (import.meta.client) {
        if (token) {
          localStorage.setItem('userToken', token)
        } else {
          localStorage.removeItem('userToken')
        }
      }

      // 同步到cookie（用于API请求）
      const tokenCookie = useCookie('token', {
        maxAge: 60 * 60 * 24 * 7,
        sameSite: 'lax',
        secure: false
      })
      tokenCookie.value = token || null
    },

    // 设置用户信息
    setUserInfo(userInfo) {
      this.userInfo = userInfo
      this.isLoggedIn = this.getIsLoggedIn

      if (import.meta.client) {
        if (userInfo && Object.keys(userInfo).length > 0) {
          localStorage.setItem('userInfo', JSON.stringify(userInfo))
        } else {
          localStorage.removeItem('userInfo')
        }
      }
    },

    // 设置完整的用户数据（登录时使用）
    setUserData(token, userInfo) {
      this.setUserToken(token)
      this.setUserInfo(userInfo)
    },

    // 清除用户数据（登出时使用）
    clearUserData() {
      this.userToken = ''
      this.userInfo = {}
      this.isLoggedIn = false

      if (import.meta.client) {
        localStorage.removeItem('userToken')
        localStorage.removeItem('userInfo')
      }

      // 清除cookie
      const tokenCookie = useCookie('token')
      tokenCookie.value = null
    },

    // 更新用户信息（从API获取最新数据）
    async updateUserInfo() {
      try {
        // const data = await getBaseUserInfoApi()
        // this.setUserInfo(data)
        console.log('更新用户信息功能待实现')
      } catch (error) {
        console.error('更新用户信息失败:', error)
      }
    },

    // 检查登录状态
    checkLoginStatus() {
      return this.getIsLoggedIn
    }
  }
})