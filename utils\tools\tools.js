// 判断对象是否为空的工具函数
export const isEmptyObject = (obj) => {
  // 非对象或数组/空值直接返回false
  if (typeof obj !== 'object' || obj === null || Array.isArray(obj)) return false;

  // 检查是否有可枚举属性
  return Object.keys(obj).length === 0;
};

//格式化手机号
export const formattedPhone = (phoneNum) => {
  return `${phoneNum.slice(0, 3)}****${phoneNum.slice(7)}`;
}

function parseTime(timeStr) {
  if (!timeStr || typeof timeStr !== 'string') return null;

  const year = timeStr.slice(0, 4);
  const month = timeStr.slice(4, 6);
  const day = timeStr.slice(6, 8);
  const hour = timeStr.slice(8, 10);
  const minute = timeStr.slice(10, 12);
  const second = timeStr.slice(12, 14);

  return new Date(
    parseInt(year),
    parseInt(month) - 1, // 月份从0开始
    parseInt(day),
    parseInt(hour),
    parseInt(minute),
    parseInt(second)
  );
}
/**
 * 
 * @param {*} date 
 * @param {YYYY-MM-DD HH:mm:ss} format 
 * @returns 
 */
export const formatTime = (date, format = 'YYYY-MM-DD HH:mm:ss') => {
  if (!date) return '';
  let formatDate = parseTime(date)
  const year = formatDate.getFullYear();
  const month = String(formatDate.getMonth() + 1).padStart(2, '0');
  const day = String(formatDate.getDate()).padStart(2, '0');
  const hour = String(formatDate.getHours()).padStart(2, '0');
  const minute = String(formatDate.getMinutes()).padStart(2, '0');
  const second = String(formatDate.getSeconds()).padStart(2, '0');

  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hour)
    .replace('mm', minute)
    .replace('ss', second);
}